/**
 * Login Component - Google OAuth Login
 */

import React, { useState, useEffect } from 'react';
import { Card, Button, Typography, Space, Alert, Spin } from 'antd';
import { GoogleOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

// Auto-login service URL (should match your NestJS service)
const AUTO_LOGIN_SERVICE_URL = process.env.REACT_APP_AUTO_LOGIN_SERVICE_URL || 'http://localhost:3000';

const Login = ({ onLoginSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [loginStatus, setLoginStatus] = useState(null);

  useEffect(() => {
    // Listen for messages from popup window
    const handleMessage = async (event) => {
      if (event.origin !== AUTO_LOGIN_SERVICE_URL) {
        return;
      }

      if (event.data.type === 'AUTH_SUCCESS') {
        setLoading(true);
        setError(null);
        
        try {
          // Get the access token from cookies or localStorage
          // Since the auth service sets HTTP-only cookies, we need to make a request to verify
          const response = await fetch(`${AUTO_LOGIN_SERVICE_URL}/auth/verify`, {
            method: 'GET',
            credentials: 'include', // Include cookies
          });

          if (response.ok) {
            const userData = await response.json();
            
            // Store user data in localStorage for the frontend
            localStorage.setItem('user', JSON.stringify(userData.user));
            localStorage.setItem('isAuthenticated', 'true');
            
            setLoginStatus({
              success: true,
              user: userData.user,
              message: 'Login successful!'
            });

            // Call the success callback
            if (onLoginSuccess) {
              onLoginSuccess(userData.user);
            }
          } else {
            throw new Error('Failed to verify authentication');
          }
        } catch (err) {
          console.error('Authentication verification failed:', err);
          setError('Authentication verification failed. Please try again.');
        } finally {
          setLoading(false);
        }
      } else if (event.data.type === 'AUTH_ERROR') {
        setLoading(false);
        setError(event.data.message || 'Authentication failed');
      }
    };

    window.addEventListener('message', handleMessage);
    
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [onLoginSuccess]);

  const handleGoogleLogin = () => {
    setLoading(true);
    setError(null);
    setLoginStatus(null);

    // Open popup window for Google OAuth
    const popup = window.open(
      `${AUTO_LOGIN_SERVICE_URL}/auth/google`,
      'google-login',
      'width=500,height=600,scrollbars=yes,resizable=yes'
    );

    // Check if popup was blocked
    if (!popup) {
      setLoading(false);
      setError('Popup was blocked. Please allow popups for this site and try again.');
      return;
    }

    // Monitor popup
    const checkClosed = setInterval(() => {
      if (popup.closed) {
        clearInterval(checkClosed);
        setLoading(false);
        if (!loginStatus) {
          setError('Login was cancelled or failed');
        }
      }
    }, 1000);
  };

  if (loginStatus && loginStatus.success) {
    return (
      <div style={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <Card 
          style={{ 
            width: 400, 
            textAlign: 'center',
            borderRadius: 16,
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
          }}
        >
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <CheckCircleOutlined style={{ fontSize: 48, color: '#52c41a' }} />
            <Title level={3} style={{ margin: 0, color: '#52c41a' }}>
              Login Successful!
            </Title>
            <Text type="secondary">
              Welcome back, {loginStatus.user.email}
            </Text>
            <Text type="secondary" style={{ fontSize: 12 }}>
              Redirecting to dashboard...
            </Text>
          </Space>
        </Card>
      </div>
    );
  }

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }}>
      <Card 
        style={{ 
          width: 400, 
          textAlign: 'center',
          borderRadius: 16,
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
        }}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Title level={2} style={{ margin: 0, color: '#667eea' }}>
              Facebook Automation
            </Title>
            <Text type="secondary">
              Sign in to access your dashboard
            </Text>
          </div>

          {error && (
            <Alert
              message="Login Error"
              description={error}
              type="error"
              icon={<ExclamationCircleOutlined />}
              showIcon
              closable
              onClose={() => setError(null)}
            />
          )}

          <Button
            type="primary"
            size="large"
            icon={<GoogleOutlined />}
            onClick={handleGoogleLogin}
            loading={loading}
            style={{
              width: '100%',
              height: 48,
              fontSize: 16,
              background: '#4285f4',
              borderColor: '#4285f4',
              borderRadius: 8,
            }}
          >
            {loading ? 'Signing in...' : 'Sign in with Google'}
          </Button>

          <div style={{ marginTop: 24 }}>
            <Text type="secondary" style={{ fontSize: 12 }}>
              By signing in, you agree to our Terms of Service and Privacy Policy.
              <br />
              You need an active account with bot Instagram package to access the features.
            </Text>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default Login;
